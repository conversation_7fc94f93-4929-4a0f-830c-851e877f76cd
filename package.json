{"name": "praeco", "version": "1.8.21", "private": true, "scripts": {"dev": "vite --host 0.0.0.0", "serve": "vite --host 0.0.0.0", "build": "vite build", "preview": "vite preview --host 0.0.0.0", "test:unit": "vitest", "test:e2e": "cypress run", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "lint:fix": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "test": "cypress run --headless", "test:coverage": "cross-env NODE_ENV=coverage nyc --reporter=lcov --reporter=text npm run test:unit", "test:coverage:watch": "cross-env NODE_ENV=coverage nyc --reporter=lcov --reporter=text npm run test:watch", "test:watch": "vitest --watch"}, "dependencies": {"@babel/core": "^7.26.0", "@browser-bunyan/server-stream": "1.8.0", "@fortawesome/fontawesome-svg-core": "6.7.2", "@fortawesome/free-brands-svg-icons": "6.7.2", "@fortawesome/free-regular-svg-icons": "6.7.2", "@fortawesome/free-solid-svg-icons": "6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "axios": "1.7.9", "browser-bunyan": "1.8.0", "change-case": "4.1.2", "comma-number": "2.1.0", "core-js": "^3.39.0", "cron-ui": "1.0.3", "dayjs": "^1.11.13", "debounce": "1.2.1", "echarts": "^5.6.0", "element-plus": "^2.9.1", "emoji-mart-vue-fast": "^15.0.3", "js-yaml": "4.1.0", "lodash.clonedeep": "4.5.0", "lodash.get": "4.4.2", "lodash.throttle": "4.1.1", "normalize.css": "8.0.1", "prettycron": "github:nsano-rururu/prettycron", "prismjs": "1.29.0", "semver": "7.6.3", "string-format": "2.0.0", "validator": "^13.12.0", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-json-pretty": "^2.4.0", "vue-native-websocket-vue3": "^3.1.6", "vue-prism-component": "^2.0.0", "vue-router": "^4.5.0", "vue-split-panel": "1.0.4", "vuex": "^4.1.0", "vuex-persist": "3.1.3", "zrender": "5.6.1"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/eslint-parser": "^7.25.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-airbnb": "^8.0.0", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.21", "axios-mock-adapter": "2.1.0", "babel-plugin-istanbul": "7.0.0", "chai": "5.1.2", "coverage-istanbul-loader": "^3.0.5", "cross-env": "7.0.3", "eslint": "^8.57.0", "eslint-plugin-cypress": "^3.6.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-rulesdir": "^0.2.2", "eslint-plugin-vue": "^9.32.0", "jsdom": "25.0.1", "localstorage-polyfill": "1.0.1", "mutationobserver-simple-polyfill": "0.0.2", "nyc": "17.1.0", "sass": "^1.83.0", "unplugin-auto-import": "^0.18.0", "unplugin-vue-components": "^0.27.0", "vite": "^5.4.0", "vitest": "^2.1.8"}, "overrides": {"@vue/vue-loader-v15": "npm:vue-loader@^15.11.1"}, "nyc": {"instrument": true, "check-coverage": true, "per-file": true, "lines": 0, "statements": 0, "functions": 0, "branches": 0, "register": "babel-register", "include": ["apollo-server/**/*.js", "src/**/*.{js,vue}"], "exclude": ["apollo-server/*.js", "src/*.js"], "reporter": ["lcov", "text", "text-summary"], "extension": [".js"], "cache": true, "all": true}}