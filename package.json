{"name": "praeco", "version": "1.8.21", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e", "lint": "vue-cli-service lint --no-fix", "lint:fix": "vue-cli-service lint", "test": "vue-cli-service test:e2e --headless", "test:coverage": "cross-env NODE_ENV=coverage nyc --reporter=lcov --reporter=text npm run test:unit", "test:coverage:watch": "cross-env NODE_ENV=coverage nyc --reporter=lcov --reporter=text npm run test:watch", "test:watch": "vue-cli-service test:unit --watch"}, "dependencies": {"@babel/core": "^7.26.0", "@browser-bunyan/server-stream": "1.8.0", "@fortawesome/fontawesome-svg-core": "6.7.2", "@fortawesome/free-brands-svg-icons": "6.7.2", "@fortawesome/free-regular-svg-icons": "6.7.2", "@fortawesome/free-solid-svg-icons": "6.7.2", "@fortawesome/vue-fontawesome": "2.0.10", "@riophae/vue-treeselect": "0.4.0", "@vue-js-cron/light": "^1.1.0", "axios": "1.7.9", "browser-bunyan": "1.8.0", "canvas": "^2.11.2", "change-case": "4.1.2", "comma-number": "2.1.0", "core-js": "^3.39.0", "cron-ui": "1.0.3", "dayjs": "^1.11.13", "debounce": "1.2.1", "echarts": "^5.6.0", "element-ui": "2.15.14", "emoji-mart-vue-fast": "^15.0.3", "js-yaml": "4.1.0", "lodash.clonedeep": "4.5.0", "lodash.get": "4.4.2", "lodash.throttle": "4.1.1", "node-sass": "^9.0.0", "normalize.css": "8.0.1", "prettycron": "github:nsano-rururu/prettycron", "prismjs": "1.29.0", "sass-loader": "^16.0.4", "semver": "7.6.3", "string-format": "2.0.0", "validator": "^13.12.0", "vue": "^2.7.16", "vue-at": "2.5.1", "vue-echarts": "7.0.3", "vue-json-pretty": "1.8.3", "vue-native-websocket": "github:nsano-rururu/vue-native-websocket#2.1.1", "vue-prism-component": "1.2.0", "vue-query-builder": "github:nsano-rururu/vue-query-builder#v0.6.1vue2", "vue-router": "3.6.5", "vue-split-panel": "1.0.4", "vuex": "3.6.2", "vuex-persist": "3.1.3", "webpack": "5.97.1", "zrender": "5.6.1"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/eslint-parser": "^7.25.9", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-e2e-cypress": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-unit-mocha": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/eslint-config-airbnb": "^8.0.0", "@vue/test-utils": "1.3.6", "axios-mock-adapter": "2.1.0", "babel-plugin-istanbul": "7.0.0", "chai": "5.1.2", "coverage-istanbul-loader": "^3.0.5", "cross-env": "7.0.3", "cypress": "^13.17.0", "eslint": "^8.57.0", "eslint-plugin-cypress": "^3.6.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-rulesdir": "^0.2.2", "eslint-plugin-vue": "^9.32.0", "jsdom": "25.0.1", "localstorage-polyfill": "1.0.1", "mutationobserver-simple-polyfill": "0.0.2", "nyc": "17.1.0", "vue-template-compiler": "^2.7.16"}, "overrides": {"@vue/vue-loader-v15": "npm:vue-loader@^15.11.1"}, "nyc": {"instrument": true, "check-coverage": true, "per-file": true, "lines": 0, "statements": 0, "functions": 0, "branches": 0, "register": "babel-register", "include": ["apollo-server/**/*.js", "src/**/*.{js,vue}"], "exclude": ["apollo-server/*.js", "src/*.js"], "reporter": ["lcov", "text", "text-summary"], "extension": [".js"], "cache": true, "all": true}}